import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  CodiGravamen,
  ContestableActDocument,
  GravamentTaxSubGroup,
  ReduccioBonificacio,
  TaxDeclararion,
  TaxSubGroupControls,
} from './models/tax-declaration.model';
import {
  Column,
  Nullable,
  SeHttpResponse,
  SeValidations,
  iDocumentPadoct,
} from 'se-ui-components-mf-lib';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { TaxDeclarationService } from './services/tax-declaration.service';
import { Router } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';
import { TaxDeclarationEndpointService } from './services/tax-declaration-endpoint.service';
import { StoreService } from '@core/services';
import { Constant } from '@core/models/constants.enum';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-tax-declaration',
  templateUrl: './tax-declaration.component.html',
  styleUrls: ['./tax-declaration.component.scss'],
})
export class TaxDeclarationComponent implements OnInit, OnDestroy {
  taxForm!: FormGroup;
  private destroyed$: Subject<void> = new Subject<void>();
  protected idTramit: Nullable<string> = '';
  protected tableColumns: Column[] =
    this.taxDeclarationService.getTableColumns();
  protected modalTableColumns: Column[] =
    this.taxDeclarationService.getModalTableColumns();
  protected showFuelBonificationFields: boolean = false;
  protected showindBonificacioInversioFields: boolean = false;
  protected readonly functionalModule: string = Constant.FUNCTIONAL_MODULE;
  protected readonly acceptedFiles = ['xlsx', 'xls', 'csv', 'ods'];
  protected readonly linkDocument: string =
    this.taxDeclarationService.getLinkDocument();
  private readonly groupByGravamen: Record<CodiGravamen, string> = {
    DIOAZU: 'dioxidSofre',
    OXINIT: 'oxidNitrogen',
    PARTIC: 'particules',
    CARORG: 'carboniOrganic',
  };

  private readonly bonificacioMap: Record<string, string> = {
    BONCOMB: 'bonificacioPercentCombustible',
    BONINVE: 'bonificacioPercentInversio',
  };

  protected readonly contestableActDocument: ContestableActDocument[] = [
    {
      type: Constant.TAX_DOCUMENT_TYPE,
      name: Constant.TAX_DOCUMENT_TYPE,
      description: this.translateService.instant(
        'SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.NOM_DOCUMENT',
      ),
      allowedFiles: this.acceptedFiles,
    },
  ];

  constructor(
    private taxDeclarationService: TaxDeclarationService,
    private router: Router,
    private fb: FormBuilder,
    private taxDeclarationEndpointService: TaxDeclarationEndpointService,
    private store: StoreService,
    private translateService: TranslateService,
  ) {
    this.createFormGroup();
  }

  ngOnInit(): void {
    this.idTramit = this.store.idTramit;
    if (!this.idTramit) return;

    this.loadTaxDeclaration();
    this.loadTipusGravament();
    this.loadBonificationsReductions();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private createFormGroup(): void {
    this.taxForm = this.fb.group({
      dioxidSofre: this.createTaxSubGroup(),
      oxidNitrogen: this.createTaxSubGroup(),
      particules: this.createTaxSubGroup(),
      carboniOrganic: this.createTaxSubGroup(),
      sumaQuotes: [
        { value: 0, disabled: true },
        [
          SeValidations.lessThan(10000000000),
          Validators.required,
          SeValidations.greaterThan(0),
        ],
      ],
      anex: this.fb.group(
        {
          idPadoct: ['', Validators.required],
          nom: ['', Validators.required],
          pes: ['', Validators.required],
          descripcio: ['', Validators.required],
          tipusDocument: ['', Validators.required],
          extension: ['', Validators.required],
        },
        Validators.required,
      ),
      indBonificacioCombustible: [false, Validators.required],
      quotaIntegraBonificable: [0.0, SeValidations.lessThan(101)],
      bonificacioPercentCombustible: { value: 50.0, disabled: true },
      bonificacioPercentAplicable: { value: 0.0, disabled: true },
      totalBonificacioCombustible: { value: 0.0, disabled: true },
      indBonificacioInversio: [false, Validators.required],
      inversioRealizada: [0.0],
      bonificacioPercentInversio: { value: 10.0, disabled: true },
      bonificacioInversioResultat: { value: 0.0, disabled: true },
      totalBonificacioInversio: { value: 0.0, disabled: true },
    });

    const includedKeys = [
      'dioxidSofre',
      'oxidNitrogen',
      'particules',
      'carboniOrganic',
    ];

    Object.keys(this.taxForm.controls)
      .filter((key) => includedKeys.includes(key))
      .forEach((groupName) => {
        this.setupTaxGroupListeners(groupName);
        this.updateSumaQuotes(groupName);
      });

    this.indBonificacioCombustibleListener();
    this.indBonificacioInversioListener();
    this.quotaIntegraBonificableListener();
    this.inversioRealizadaListener();
  }

  private quotaIntegraBonificableListener(): void {
    this.taxForm
      .get('quotaIntegraBonificable')
      ?.valueChanges.pipe(
        takeUntil(this.destroyed$),
        distinctUntilChanged(),
        debounceTime(300),
      )
      .subscribe((value: number) => {
        console.log(this.taxForm.get('bonificacioPercentCombustible')?.value);

        const bonificacioPercentCombustible = this.getCorrectRounding(
          (value * this.taxForm.get('bonificacioPercentCombustible')?.value) /
            100,
        );
        const sumaQuotes = this.taxForm.get('sumaQuotes')?.value || 0;
        this.taxForm
          .get('bonificacioPercentAplicable')
          ?.setValue(bonificacioPercentCombustible);
        this.taxForm
          .get('totalBonificacioCombustible')
          ?.setValue(
            this.getCorrectRounding(
              (sumaQuotes * bonificacioPercentCombustible) / 100,
            ),
          );
      });
  }

  private inversioRealizadaListener(): void {
    this.taxForm
      .get('inversioRealizada')
      ?.valueChanges.pipe(
        takeUntil(this.destroyed$),
        distinctUntilChanged(),
        debounceTime(300),
      )
      .subscribe((value: number) => {
        const bonificacioInversioResultat = this.getCorrectRounding(
          (value * 10) / 100,
        );
        const sumaQuotes = this.taxForm.get('sumaQuotes')?.value || 0;
        this.taxForm
          .get('bonificacioInversioResultat')
          ?.setValue(bonificacioInversioResultat);

        const bonusCap = this.getCorrectRounding((sumaQuotes * 15) / 100);
        const finalBonification = Math.min(
          bonificacioInversioResultat,
          bonusCap,
        );

        this.taxForm
          .get('totalBonificacioInversio')
          ?.setValue(value > 0 ? finalBonification : 0);
      });
  }

  private indBonificacioCombustibleListener(): void {
    this.taxForm
      .get('indBonificacioCombustible')
      ?.valueChanges.pipe(takeUntil(this.destroyed$))
      .subscribe((value: boolean) => {
        this.showFuelBonificationFields = !!value;
        if (!value) {
          this.taxForm.get('quotaIntegraBonificable')?.setValue(0);
          this.taxForm.get('bonificacioPercentAplicable')?.setValue(0);
          this.taxForm.get('totalBonificacioCombustible')?.setValue(0);
        }
      });
  }

  private indBonificacioInversioListener(): void {
    this.taxForm
      .get('indBonificacioInversio')
      ?.valueChanges.pipe(takeUntil(this.destroyed$))
      .subscribe((value: boolean) => {
        this.showindBonificacioInversioFields = !!value;
        if (!value) {
          this.taxForm.get('inversioRealizada')?.setValue(0);
          this.taxForm.get('bonificacioInversioResultat')?.setValue(0);
          this.taxForm.get('totalBonificacioInversio')?.setValue(0);
        }
      });
  }

  protected onFilesLoaded(event: Event): void {
    console.log('Files loaded:', event);

    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];
    if (documentsAdded.length > 0) {
      const anexData = documentsAdded[0];
      this.taxForm.get('anex')?.patchValue({
        idPadoct: anexData.idPadoct,
        nom: anexData.nom,
        descripcio: anexData.description,
        pes: anexData.size,
        extension: anexData.format?.split('/')[1],
        tipusDocument: Constant.TAX_DOCUMENT_TYPE,
      });
    } else {
      this.taxForm.get('anex')?.reset();
    }
  }

  private loadTaxDeclaration(): void {
    this.taxDeclarationEndpointService
      .getDeclaration(this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response: SeHttpResponse) => {
        if (response.content) {
          this.taxForm.patchValue(response.content);
          this.showFuelBonificationFields =
            !!response.content.indBonificacioCombustible;
          this.showindBonificacioInversioFields =
            !!response.content.indBonificacioInversio;
        }
      });
  }

  private loadTipusGravament(): void {
    this.taxDeclarationEndpointService
      .getTipusGravament(
        Constant.NAME,
        this.store.taxYear?.toString() || '',
        Constant.ANUAL_PERIOD,
      )
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response: SeHttpResponse<GravamentTaxSubGroup[]>) => {
        if (response.content) {
          this.processGravamentResponse(response.content);
        }
      });
  }

  private processGravamentResponse(content: GravamentTaxSubGroup[]): void {
    content.forEach((item) => {
      const groupName = this.groupByGravamen[item.codiGravamen];
      const group = this.taxForm.get(
        groupName,
      ) as FormGroup<TaxSubGroupControls>;
      group?.get('tipusGravament')?.setValue(item.importTarifa);
    });
  }

  private processReduccionsBonificacions(
    data: ReduccioBonificacio[],
    groupByGravamen: Record<CodiGravamen, string>,
    bonificacioMap: Record<string, string>,
  ): void {
    data.forEach((item) => {
      if (item.tipus === 'REDUCCIONS') {
        const groupName = groupByGravamen[item.codi as CodiGravamen];
        if (groupName) {
          const group = this.taxForm.get(
            groupName,
          ) as FormGroup<TaxSubGroupControls>;
          group.get('reduccio')?.setValue(item.valor);
        }
      } else if (item.tipus === 'BONIFICACIONS') {
        const controlName = bonificacioMap[item.codi];
        if (controlName) {
          this.taxForm.get(controlName)?.setValue(item.valor * 100);
        }
      }
    });
  }

  private loadBonificationsReductions(): void {
    this.taxDeclarationEndpointService
      .getDadesMestres(this.idTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response: SeHttpResponse) => {
        if (response.content) {
          this.processReduccionsBonificacions(
            response.content,
            this.groupByGravamen,
            this.bonificacioMap,
          );
        }
      });
  }

  private setupTaxGroupListeners(groupName: string): void {
    const group = this.taxForm.get(groupName) as FormGroup<TaxSubGroupControls>;
    const {
      baseImposable,
      reduccio,
      tipusGravament,
      baseLiquidable,
      quotaIntegra,
    } = group.controls;

    baseImposable?.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
        distinctUntilChanged(),
        debounceTime(300),
      )
      .subscribe((value) => {
        const baseImposableValue = value || 0;
        const reduccioValue = reduccio?.value || 0;
        const baseImposableMinusReduccio = baseImposableValue - reduccioValue;
        const baseLiquidableValue =
          baseImposableMinusReduccio <= 0
            ? 0
            : this.getCorrectRounding(baseImposableMinusReduccio);
        baseLiquidable?.setValue(baseLiquidableValue, { emitEvent: false });
        quotaIntegra?.setValue(
          this.getCorrectRounding(
            baseLiquidableValue * (tipusGravament?.value || 0),
          ),
        );
      });
  }

  private updateSumaQuotes(groupName: string): void {
    const group = this.taxForm.get(groupName) as FormGroup<TaxSubGroupControls>;
    const { quotaIntegra } = group.controls;

    quotaIntegra?.valueChanges
      .pipe(
        takeUntil(this.destroyed$),
        distinctUntilChanged(),
        debounceTime(300),
      )
      .subscribe(() => {
        const taxGroups = [
          'dioxidSofre',
          'oxidNitrogen',
          'particules',
          'carboniOrganic',
        ];
        let total = 0;

        taxGroups.forEach((groupName) => {
          const quotaIntegra =
            this.taxForm.get(groupName)?.get('quotaIntegra')?.value || 0;
          total += quotaIntegra;
        });

        this.taxForm.get('sumaQuotes')?.setValue(total);
        if (total <= 0) {
          return;
        }
        this.calculateAndSetBonus(total, {
          conditionField: 'quotaIntegraBonificable',
          percentField: 'bonificacioPercentCombustible',
          resultField: 'totalBonificacioCombustible',
        });

        this.calculateAndSetBonus(total, {
          conditionField: 'inversioRealizada',
          percentField: 'bonificacioPercentInversio',
          resultField: 'totalBonificacioInversio',
        });
      });
  }

  private calculateAndSetBonus(
    total: number,
    config: {
      conditionField: string;
      percentField: string;
      resultField: string;
    },
  ): void {
    const { conditionField, percentField, resultField } = config;

    if (this.taxForm.get(conditionField)?.value > 0) {
      const percentValue = this.taxForm.get(percentField)?.value;
      let calculatedValue = this.getCorrectRounding(
        (total * percentValue) / 100,
      );
      if (percentValue === 10) {
        const maxCap = this.getCorrectRounding((total * 15) / 100);
        calculatedValue = Math.min(calculatedValue, maxCap);
      }
      this.taxForm.get(resultField)?.setValue(calculatedValue);
    }
  }

  private getCorrectRounding(value: number): number {
    return Math.round((value + Number.EPSILON) * 100) / 100;
  }

  private createTaxSubGroup(): FormGroup<TaxSubGroupControls> {
    return new FormGroup({
      baseImposable: new FormControl<number | null>(
        0.00,
        SeValidations.lessThan(10000000000),
      ),
      reduccio: new FormControl<number | null>(
        { value: null, disabled: true },
        Validators.required,
      ),
      baseLiquidable: new FormControl<number | null>(
        { value: null, disabled: true },
        SeValidations.lessThan(10000000000),
      ),
      tipusGravament: new FormControl<number | null>(
        { value: null, disabled: true },
        Validators.required,
      ),
      quotaIntegra: new FormControl<number | null>(
        { value: null, disabled: true },
        Validators.required,
      ),
    });
  }

  protected isFormValid(): boolean {
    return this.taxForm.valid && this.taxForm.get('sumaQuotes')?.value > 0;
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.YEAR_INSTALLATION]);
  }

  protected submit(): void {
    const request: TaxDeclararion = this.taxForm.getRawValue();
    request.idTramit = this.store.idTramit!;
    this.taxDeclarationEndpointService
      .postDeclaration(request)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response: SeHttpResponse) => {
        if (response.content) {
          this.router.navigate([AppRoutes.SUMMARY_CALCULATION]);
        } else {
          console.error('Error submitting declaration:', response);
        }
      });
  }
}
