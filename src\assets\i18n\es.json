{"SE_GASOS_MF": {"APP_TITLE": "Impuesto sobre la emisión de gases y partículas a la atmósfera producida por la industria", "STEPPER": {"PARTICIPANTS": "Intervinientes", "YEAR_INSTALLATION": "Ejercicio e instalación", "TAX_DECLARATION": "Declaración", "SUMMARY_CALCULATION": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "HEADER_INFO": {"TAXPAYER": "Declarante:", "EXERCICI": "<PERSON><PERSON><PERSON><PERSON>: ", "MODEL": "Modelo:", "EMIS_CODE": "Instalación: ", "COMPLEMENTARY_OF": "Complementaria de ", "STATE": {"IN_PROGRESS": "En curso", "PRESENTAT": "Presentada", "PAGAT": "Presentada y pagada", "PENDENT_PAGAMENT": "Pago pendiente"}}, "MODULE_WORKING_SESSION": {"SELF_ASSESSMENTS": {"ACTIONS": {"OBTAIN_PAYMENT_LETTER": "Obtener carta de pago a pagar en entidad bancaria", "APPLY_FOR_FRACTIONATION": "Solicitar aplazamiento o fraccionamiento"}}, "W_S": {"DESCRIPTION": "Seleccione la sesión de trabajo que desea continuar.", "TABLE_HEADERS": {"SESSION_DATE": "Fecha de  edición", "DECLARANT": "Declarante (sujeto pasivo)", "YEAR": "<PERSON><PERSON><PERSON><PERSON>", "EMIS_CODE": "Código de instalación (EMIS)", "ACTIONS": "Acciones"}}}, "MODULE_PARTICIPANTS": {"DESCRIPTION_DATA_TO_ENTER": "Introduzca el nombre y apellidos del declarante o la razón social, y el NIF.", "TAXPAYER_NAME_LABEL": "Apellidos y nombre y / Razón social"}, "MODULE_YEAR_INSTALLATION": {"PANEL_DESCRIPTION": "Seleccione el ejercicio y el período que desea declarar.", "WORKING_SESSION": {"MODAL": {"TITLE": "Sesión de trabajo iniciada", "DETAIL": "Tiene una sesión de trabajo iniciada para este declarante, ejercicio e instalación. ¿Desea continuar con la sesión actual o recuperar la sesión anterior?", "CANCEL_BUTTON": "Continuar con la sesión actual", "ACCEPT_BUTTON": "Recuperar la sesión anterior"}}, "PANEL_INSTALATION": {"TITLE": "Identificación de la instalación", "DESCRIPTION_1": "Introduzca el código EMIS de la instalación que declarará:", "DESCRIPTION_2": "El código EMIS es un número interno que identifica a los establecimientos y que se mantiene aunque cambien de nombre.", "DESCRIPTION_3": "Si no dispone de este código, debe solicitarlo a la Sección de Control de Emisiones del Servicio de Vigilancia y Control del Aire (Departamento de Territorio y Sostenibilidad), en el teléfono 93 445 000 o en la dirección electrónica ", "EMIS_CODE": "Código de instalción (EMIS)"}}, "MODULE_TAX_DECLARATION": {"ALERT_INFO": "En caso de que no finalice el trámite, podrá recuperar la sesión de trabajo iniciada con los datos que haya introducido.", "MASS_EMISSIONS": {"PANEL_TITLE": "Emisiones másicas", "PANEL_DESCRIPTION": "Para cada contaminante debe introducir la base imponible en toneladas (t). La base imponible es la carga másica anual que se emite en la atmósfera.", "SULPHUR_DIOXIDE": "Dióxido de azufre", "NITROGEN_OXIDES": "Óxidos de nitrógeno", "PARTICLES": "Partículas", "TOTAL_ORGANIC_CARBON": "Carbono orgánico total", "BASE_IMPOSABLE": "Base imponible", "REDUCCIO": "Reducción", "REDUCCIO_TOOLTIP": "Se aplican automáticamente las toneladas de reducción de cada tipo de contaminante.", "BASE_LIQUIDABLE": "Base liquidable", "BASE_LIQUIDABLE_TOOLTIP": "La base liquidable se calcula automáticamente y se obtiene restando de la base imponible las toneladas de la reducción.", "TIPUS_GRAVAMENT": "Tipo de gravamen", "QUOTA_INTEGRA": "<PERSON><PERSON><PERSON>", "QUOTA_INTEGRA_TOOLTIP": "La cuota íntegra se calcula automáticamente y se obtiene multiplicando la base liquidable por el tipo de gravamen.", "TOTAL_AMOUNT": "Suma de cuotas (Cuotas íntegras)"}, "DOCUMENTS": {"TITLE": "Documentación a aportar", "DESCRIPTION": "Adjunte <a href={{LINK}} target='blank'>el anexo del modelo 980</a>", "DESCRIPTION_2": " con el detalle de las emisiones másicas de los focos de cada instalación.", "NOM_DOCUMENT": "Lista", "UPLOAD_DESCRIPTION": "El documento aportado ha de estar en el formato XLSX / XLS / CSV / ODS, en total no puede superar los 125 Mb."}, "BONUSES": {"TITLE": "Bonificaciones", "SUBTITLE": "Bonificación por combustible", "TOOLTIP": "Para obtener la cuota líquida se aplica sobre la cuota íntegra las siguientes bonificaciones.", "DESCRIPTION": "Es una bonificación del 50% que se aplica sobre la cuota íntegra bonificable. La cuota íntegra bonificable es el porcentaje que representa la carga másica anual de las instalaciones de cogeneración ubicadas en establecimientos industriales, con una potencia nominal superior a 20 megavatios térmicos, que utilizan como combustible gas natural o biogás, respecto de la carga másica anual del establecimiento. Para poder aplicar esta bonificación, el contenido de azufre del biogás no puede ser superior al establecido por la normativa para el gas natural.", "FUEL_SWITCH": "Utilización de combustibles más limpios", "FUEL": {"QUOTA_INTEGRA": "% Cuota íntegra bonificable", "QUOTA_INTEGRA_TOOLTIP": "El usuario debe introducirla manualmente", "BONUS": "Bonificación", "BONUS_APLICABLE": "% Bonificación aplicable", "TOTAL_BONUS": "Bonificació", "TOTAL_BONUS_TOOLTIP": "Importe resultante de multiplicar %bonificación aplicable por la cuota íntegra bonificable."}, "INVESTMENT": {"TITLE": "Bonificación por inversión", "DESCRIPTION": "Es una bonificación del 10% de las inversiones realizadas para reducir las emisiones contaminantes atmosféricas canalizadas, certificadas por la Dirección General de Calidad Ambiental, con un límite del 15% de la cuota íntegra.", "INVESTMENT_SWITCH": "Inversiones en tecnología de reducción de emisiones", "INVESTMENT_AMOUNT": "Inversión realizada", "BONUS_APPLICABLE": "% Bonificación", "RESULT": "<PERSON><PERSON><PERSON><PERSON>", "RESULT_TOOLTIP": "Aplicación del 10% de bonificación de la inversión realizada.", "BONUS": "Bonificación", "BONUS_TOOLTIP": "La bonificación por inversión es el 10% de la inversión realizada, con un tope del 15% de la cuota íntegra."}}}, "MODULE_CALCULATION": {"TEMPLATE": {"QUOTA": "<PERSON><PERSON>ta integra (suma de cuotas)", "BONIFICATIONS": "Bonificaciones", "LIQUID": "<PERSON><PERSON><PERSON> l<PERSON>a", "PREVIOUS": "Cuota líquida liquidada anteriormente", "RESULT": "Cuota resultante (Cuota líquida - Cuota líquida liquidada anteriormente)", "SURCHARGE": "Recargos por presentación fuera de plazo", "INTEREST": "Intereses de demora por presentación fuera de plazo"}, "ALERTS": {"DISABLED_COMPLEMENTARY": "A partir de los datos introducidos, el resultado es una cuota negativa. En este caso no puede hacer una autoliquidación complementaria, sino que tiene que solicitar una rectificación de la autoliquidación."}}, "MODULE_PRESENTACION_I_PAGAMENT": {"PAGAMENT": {"MODAL_MESSAGE": "Autoliquidación en proceso de pago..."}, "PRESENTACIO": {"MODAL_MESSAGE": "Autoliquidación en proceso de presentación...", "MODAL_MESSAGE_PAGAMENT": "Autoliquidación en proceso de presentación y pago..."}, "DOCUMENTATION": {"ALERT_TITLE_SUCCESS": "Presentación realizada correctamente", "ALERT_DESCRIPTION_PAGAT_SUCCESS": "Le confirmamos que ha presentado la declaración (autoliquidación) y que la ha pagado. Se he generado un número de justificante con relación a su declaración y la documentación que ha aportado. Puede descargarse el justificante a continuación.", "ALERT_DESCRIPTION_PRESENTAT_PENDENT_SUCCESS": "Le confirmamos que ha presentado la declaración (autoliquidación). Se he generado un número de justificante con relación a su declaración y la documentación que ha aportado. Puede descargarse el justificante a continuación.", "ALERT_TITLE_PRESENTACIO_ERROR": "La autoliquidación no se ha podido presentar", "ALERT_DESCRIPTION_PRESENTACIO_ERROR": "Se ha producido un error en la presentación de la declaración (autoliquidación). Vuelva a intentarlo más tarde, por favor.", "ALERT_TITLE_PAGAMENT_ERROR": "Le confirmamos que ha presentado la declaración (autoliquidación). Se ha generado un número de justificante con relación a su declaración y la documentación que ha aportado. Puede descargarse el justificante a continuación. Tenga en cuenta que no ha pagado la declaración por error de pago:", "ALERT_DESCRIPTION_PAGAMENT_ERROR": "Puede reintentar el pago mediante las opciones de la siguiente tabla:"}}, "MODULE_RESULTAT": {"TABLE_ACTIONS": {"PAYMENT_POSTPONEMENT": "Solicitar aplazamiento o fraccionamiento", "VIEW_SELFASSESMENT": "Consultar autoliquidación"}}}}