import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppRoutes } from '@core/models/app-routes.enum';

const routes: Routes = [
  {
    path: '',
    redirectTo: AppRoutes.WORKING_SESSION,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.ROOT,
    redirectTo: AppRoutes.WORKING_SESSION,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.WORKING_SESSION,
    loadChildren: () =>
      import(`./modules/working-session/working-session.module`).then(
        (module) => module.WorkingSessionModule,
      ),
  },
  {
    path: AppRoutes.PARTICIPANTS,
    loadChildren: () =>
      import(`./modules/participants/participants.module`).then(
        (module) => module.ParticipantsModule,
      ),
  },
  {
    path: AppRoutes.YEAR_INSTALLATION,
    loadChildren: () =>
      import(`./modules/year-installation/year-installation.module`).then(
        (module) => module.YearInstallationModule,
      ),
  },
  {
    path: AppRoutes.TAX_DECLARATION,
    loadChildren: () =>
      import(`./modules/tax-declaration/tax-declaration.module`).then(
        (module) => module.TaxDeclarationModule,
      ),
  },
  {
    path: AppRoutes.SUMMARY_CALCULATION,
    loadChildren: () =>
      import(`./modules/calculation/calculation.module`).then(
        (module) => module.CalculationModule,
      ),
  },
  {
    path: AppRoutes.PRESENTATION,
    loadChildren: () =>
      import(
        `./modules/presentacio-i-pagament/presentacio-i-pagament.module`
      ).then((module) => module.PresentacioIPagamentModule),
  },
  {
    path: AppRoutes.RESULT,
    loadChildren: () =>
      import(
        `./modules/presentacio-i-pagament/presentacio-i-pagament.module`
      ).then((module) => module.PresentacioIPagamentModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
